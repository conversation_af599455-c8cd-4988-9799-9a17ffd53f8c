<?php
/** @noinspection PhpUnused */
declare(strict_types=1);

namespace app\common\queue;

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;
use think\facade\Log;
use Throwable;

/**
 * RabbitMQ 消息消费者基类
 *
 * 提供基础的消费者功能，支持死信队列
 * 子类只需要实现 processMessage 方法即可
 */
abstract class BaseConsumer
{
    // 处理结果常量
    public const RESULT_SUCCESS = 'success';  // 处理成功，确认消息
    public const RESULT_FAILED = 'failed';    // 处理失败，进入死信队列
    public const RESULT_RETRY = 'retry';      // 处理失败，重新入队
    public const RESULT_SKIP = 'skip';        // 跳过消息，确认但不重试
    public const RESULT_DELAY_RETRY = 'delay_retry'; // 延时重试

    protected AMQPStreamConnection $connection;
    protected AMQPChannel $channel;
    protected string $exchangeName;
    protected string $exchangeType;
    protected string $queueName;
    protected string $routingKey;

    // 延时重试配置
    protected bool $enableDelayRetry = false;     // 是否启用延时重试
    protected int $maxRetryCount = 3;             // 最大重试次数
    protected array $retryDelays = [30, 60, 300]; // 重试延时时间（秒）

    /**
     * 构造函数
     *
     * @param string $exchangeName 交换机名称
     * @param string $queueName 队列名称
     * @param string $exchangeType 交换机类型
     * @param string $routingKey 路由键
     */
    public function __construct(string $exchangeName, string $queueName, string $exchangeType = 'direct', string $routingKey = '')
    {
        $this->exchangeName = $exchangeName;
        $this->queueName = $queueName;
        $this->exchangeType = $exchangeType;
        $this->routingKey = $routingKey;
    }

    /**
     * 启用延时重试功能
     *
     * @param array $retryDelays 重试延时时间数组（秒）
     */
    protected function enableDelayRetry(array $retryDelays = [30, 60, 300]): void
    {
        $this->enableDelayRetry = true;
        $this->maxRetryCount = count($retryDelays) ;
        $this->retryDelays = $retryDelays;
    }

    /**
     * 启动消费者
     */
    public function start(): void
    {
        try {
            $this->connect();
            $this->setupExchangeAndQueue();
            $this->consume();
        } catch (Throwable $e) {
            Log::error(sprintf('消费者启动失败: %s', $e->getMessage()));
            throw $e;
        }
    }

    /**
     * 建立 RabbitMQ 连接
     */
    private function connect(): void
    {
        try {
            // 从配置文件获取 RabbitMQ 连接配置
            $config = config('rabbitmq.connection');

            $this->connection = new AMQPStreamConnection(
                $config['host'],
                $config['port'],
                $config['user'],
                $config['password'],
                $config['vhost'],
                false, // 坚持连接
                'AMQPLAIN', // 登录方法
                null, // 登录响应
                'en_US', // 区域设置
                $config['connection_timeout'],
                $config['read_write_timeout'],
                null, // 上下文
                $config['keepalive'],
                $config['heartbeat']
            );

            $this->channel = $this->connection->channel();
            Log::info('RabbitMQ 连接建立成功');
        } catch (Throwable $e) {
            Log::error('RabbitMQ Consumer 连接失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 设置交换机和队列（包含死信队列）
     */
    private function setupExchangeAndQueue(): void
    {
        // 根据是否启用延时重试来决定交换机类型
        if ($this->enableDelayRetry) {
            // 声明延时交换机（使用 x-delayed-message 类型）
            $this->channel->exchange_declare(
                $this->exchangeName,
                'x-delayed-message',
                false,  // 被动模式
                true,   // 持久化
                false,  // 自动删除
                false,  // internal
                false,  // nowait
                new AMQPTable(['x-delayed-type' => $this->exchangeType])
            );
        } else {
            // 声明普通交换机
            $this->channel->exchange_declare(
                $this->exchangeName,
                $this->exchangeType,
                false,  // 被动模式
                true,   // 持久化
                false   // 自动删除
            );
        }

        // 设置死信队列相关参数
        $deadLetterExchange = $this->exchangeName . '.dead';
        $deadLetterQueue = $this->queueName . '.dead';
        $deadLetterRoutingKey = $this->routingKey ? $this->routingKey . '.dead' : $deadLetterQueue;

        // 设置死信队列
        $this->setupDeadLetterQueue($deadLetterExchange, $deadLetterQueue, $deadLetterRoutingKey);

        // 声明主队列，配置死信参数
        $this->channel->queue_declare(
            $this->queueName,
            false,  // 被动模式
            true,   // 持久化
            false,  // 排他性
            false,  // 自动删除
            false,  // 不等待
            new AMQPTable([
                'x-dead-letter-exchange' => $deadLetterExchange,
                'x-dead-letter-routing-key' => $deadLetterRoutingKey
            ])
        );

        // 绑定主队列到交换机
        if ($this->routingKey !== '') {
            $this->channel->queue_bind($this->queueName, $this->exchangeName, $this->routingKey);
        } else {
            $this->channel->queue_bind($this->queueName, $this->exchangeName);
        }

        Log::info(sprintf('交换机 [%s] 和队列 [%s] 设置完成', $this->exchangeName, $this->queueName));
        Log::info(sprintf('死信队列 [%s] 已配置', $deadLetterQueue));
        if ($this->enableDelayRetry) {
            Log::info('延时重试功能已启用，交换机类型: x-delayed-message');
        }
    }

    /**
     * 设置死信队列
     *
     * @param string $deadLetterExchange 死信交换机名称
     * @param string $deadLetterQueue 死信队列名称
     * @param string $deadLetterRoutingKey 死信路由键
     */
    private function setupDeadLetterQueue(string $deadLetterExchange, string $deadLetterQueue, string $deadLetterRoutingKey): void
    {
        // 声明死信交换机
        $this->channel->exchange_declare(
            $deadLetterExchange,
            'direct',  // 死信交换机使用direct类型
            false,     // 被动模式
            true,      // 持久化
            false      // 自动删除
        );

        // 声明死信队列
        $this->channel->queue_declare(
            $deadLetterQueue,
            false,  // 被动模式
            true,   // 持久化
            false,  // 排他性
            false   // 自动删除
        );

        // 绑定死信队列到死信交换机
        $this->channel->queue_bind(
            $deadLetterQueue,
            $deadLetterExchange,
            $deadLetterRoutingKey
        );

        Log::info("死信交换机 {$deadLetterExchange} 和死信队列 {$deadLetterQueue} 已设置完成");
    }

    /**
     * 开始消费消息
     */
    private function consume(): void
    {
        // 设置 QoS，一次只处理一条消息
        $this->channel->basic_qos(null, 1, null);

        // 设置消费回调
        $this->channel->basic_consume(
            $this->queueName,
            '',     // 消费者标签
            false,  // 不接收本地发布的消息
            false,  // 手动确认
            false,  // 非排他性
            false,  // 不等待
            [$this, 'handleMessage']
        );

        Log::info(sprintf('开始消费队列 [%s] 的消息...', $this->queueName));

        // 持续监听消息
        while ($this->channel->is_consuming()) {
            $this->channel->wait();
        }
    }

    /**
     * 处理接收到的消息
     *
     * @param AMQPMessage $message
     */
    public function handleMessage(AMQPMessage $message): void
    {
        $messageBody = $message->getBody();
        $deliveryTag = $message->getDeliveryTag();

        Log::info(sprintf('收到消息: %s', $messageBody));

        try {
            $result = $this->processMessage($messageBody);
        } catch (Throwable $e) {
            Log::error(sprintf('处理消息异常: %s', $e->getMessage()));
            $result = self::RESULT_FAILED;
        }

        // 根据处理结果确认或拒绝消息
        switch ($result) {
            case self::RESULT_SUCCESS:
                // 确认消息处理成功
                $this->channel->basic_ack($deliveryTag);
                Log::info('消息处理成功，已确认');
                break;

            case self::RESULT_SKIP:
                // 跳过消息，不重新入队（用于格式错误等不可恢复的情况）
                $this->channel->basic_ack($deliveryTag);
                Log::warning('消息已跳过');
                break;

            case self::RESULT_DELAY_RETRY:
                // 延时重试处理
                if ($this->enableDelayRetry) {
                    $this->handleDelayRetry($message);
                } else {
                    // 如果未启用延时重试，则进入死信队列
                    $this->channel->basic_nack($deliveryTag, false, false);
                    Log::error('消息处理失败，延时重试未启用，已进入死信队列');
                }
                break;

            case self::RESULT_FAILED:
                // 拒绝消息，进入死信队列（不重新入队）
                $this->channel->basic_nack($deliveryTag, false, false);
                Log::error('消息处理失败，已进入死信队列');
                break;

            case self::RESULT_RETRY:
            default:
                // 拒绝消息，重新入队等待消费
                $this->channel->basic_nack($deliveryTag, false, true);
                Log::warning('消息处理失败，已重新入队等待消费');
                break;
        }
    }

    /**
     * 处理延时重试
     *
     * @param AMQPMessage $message
     * @throws Throwable
     */
    private function handleDelayRetry(AMQPMessage $message): void
    {
        $messageBody = $message->getBody();
        $deliveryTag = $message->getDeliveryTag();

        // 解析消息获取重试信息
        $messageData = json_decode($messageBody, true);
        if (!$messageData) {
            Log::error('延时重试：消息格式错误，进入死信队列');
            $this->channel->basic_nack($deliveryTag, false, false);
            return;
        }
        // 如果没有_retry_count字段，则说明是第一次重试
        if (!isset($messageData['_retry_count'])) {
            $messageData['_retry_count'] = 0;
            $messageData['_retry_time'] = time();
        }

        // 获取当前重试次数
        $retryCount = $messageData['_retry_count'];

        // 检查是否超过最大重试次数
        if ($retryCount >= $this->maxRetryCount) {
            Log::error(sprintf('延时重试：已达到最大重试次数 %d，进入死信队列', $this->maxRetryCount));
            $this->channel->basic_nack($deliveryTag, false, false);
            return;
        }

        // 获取本次应该的延时时间
        $delaySeconds = $this->retryDelays[$retryCount];

        // 更新重试次数
        $messageData['_retry_count'] = $retryCount + 1;
        $messageData['_retry_time'] = time();

        // 发布延时消息
        $this->publishDelayMessage($messageData, $delaySeconds);

        // 确认原消息
        $this->channel->basic_ack($deliveryTag);

        Log::info(sprintf('延时重试：第 %d 次重试，延时 %d 秒后重新处理', $retryCount + 1, $delaySeconds));
    }

    /**
     * 重新发布延时消息(重试)
     *
     * @param array $messageData 消息数据
     * @param int $delaySeconds 延时秒数
     */
    private function publishDelayMessage(array $messageData, int $delaySeconds): void
    {
        try {
            // 创建延时消息
            $delayMessage = new AMQPMessage(
                json_encode($messageData, JSON_UNESCAPED_UNICODE),
                [
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT,
                    'content_type' => 'application/json',
                    'timestamp' => time(),
                    'application_headers' => new AMQPTable([
                        'x-delay' => $delaySeconds * 1000 // 延时时间（毫秒）
                    ])
                ]
            );

            // 发布延时消息到交换机
            $this->channel->basic_publish($delayMessage, $this->exchangeName, $this->routingKey);

            Log::info(sprintf('重试消息已发布到交换机 %s，延时 %d 秒', $this->exchangeName, $delaySeconds));

        } catch (Throwable $e) {
            Log::error('重试消息发布失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 处理消息的业务逻辑（子类实现）
     *
     * @param string $messageBody 消息内容
     * @return string 处理结果常量
     */
    abstract protected function processMessage(string $messageBody): string;

    /**
     * 关闭连接
     */
    public function close(): void
    {
        try {
            if (isset($this->channel)) {
                $this->channel->close();
            }
            if (isset($this->connection)) {
                $this->connection->close();
            }
            Log::info('RabbitMQ 连接已关闭');
        } catch (Throwable $e) {
            Log::error('RabbitMQ Consumer 关闭连接时出错: ' . $e->getMessage());
        }
    }

    /**
     * 析构函数，确保连接被关闭
     */
    public function __destruct()
    {
        $this->close();
    }
}
