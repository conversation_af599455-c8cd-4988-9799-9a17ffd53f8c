<?php
declare(strict_types=1);

namespace app\command;

use app\ms\SendJob;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Log;
use Throwable;

/**
 * API 同步 RabbitMQ 消费者命令
 * 
 * 用于启动 API 同步任务的 RabbitMQ 消费者
 * 命令：php think api:sync-consumer
 */
class ApiSyncCommand extends Command
{
    /**
     * 配置命令
     */
    protected function configure(): void
    {
        $this->setName('api:sync-consumer')
            ->setDescription('启动 API 同步 RabbitMQ 消费者');
    }

    /**
     * 执行命令
     *
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        $output->writeln('启动 API 同步 RabbitMQ 消费者...');
        Log::info('API 同步 RabbitMQ 消费者启动');

        try {
            // 创建并启动消费者
            $consumer = new SendJob();
            $consumer->start();
        } catch (Throwable $e) {
            $errorMsg = sprintf(
                'API 同步消费者启动失败: %s (文件: %s, 行: %d)',
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );
            
            $output->writeln('<error>' . $errorMsg . '</error>');
            Log::error($errorMsg);
            Log::error('异常堆栈: ' . $e->getTraceAsString());
        }
    }
}
