<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

//use app\common\controller\ApiBase;
//use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use app\common\model\PayOrder as PayOrderModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\facade\Log;
use think\response\Json;
use Throwable;

#[Apidoc\Title("财务中心/充值订单")]
class RechargeOrder extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new PayOrderModel();
    }

    #[
        Apidoc\Title("充值订单列表"),
        Apidoc\Author("lwj 2024.6.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/RechargeOrder/refund_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "充值流水号"),
        Apidoc\Param(name: "user_id", type: "int", desc: "用户id"),
        Apidoc\Param(name: "trade_no", type: "string", desc: "外部订单编号"),
        Apidoc\Param(name: "list_id", type: "string", desc: "支付项id"),
        Apidoc\Param(name: "state", type: "array|int", desc: "状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付"),
        Apidoc\Param(name: "add_balance", type: "array|int", desc: "加余额结果，1成功，2失败，3没有加币（默认3）"),
        Apidoc\Param(name: "type", type: "array|int", desc: "支付类型，1充值余额，2充卡"),
        Apidoc\Param(name: "refund_state", type: "array|int", desc: "退款状态，1无退款，2申请退款，3已退款，4退款失败，10已拒绝，11已取消"),
        Apidoc\Param(name: "time_range", type: "array", desc: "日期范围"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '充值流水号'],
            ['name' => 'trade_no', 'type' => 'string', 'desc' => '外部订单编号'],
            ['name' => 'name', 'type' => 'string', 'desc' => '名称'],
            ['name' => 'price', 'type' => 'int', 'desc' => '支付金额'],
            ['name' => 'currency', 'type' => 'int', 'desc' => '币种：1:CNY,2:USD'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付'],
            ['name' => 'add_balance', 'type' => 'int', 'desc' => '加余额结果，1成功，2失败，3没有加币（默认3）'],
            ['name' => 'type', 'type' => 'int', 'desc' => '支付类型，1充值余额，2充卡'],
            ['name' => 'refund_state', 'type' => 'int', 'desc' => '退款状态，1无退款，2申请退款，3已退款，4退款失败，10已拒绝，11已取消'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],

            ['name' => 'phone', 'type' => 'string', 'desc' => '用户手机号'],
            ['name' => 'title', 'type' => 'string', 'desc' => '充值项'],
        ]),
    ]
    public function refund_order_list(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }
        $data = $this->request->post();
        // 将时间范围转为开始时间和结束时间
        if (!empty($data['time_range']) &&
            is_array($data['time_range']) &&
            count($data['time_range']) == 2
        ) {
            $data['start_time'] = $data['time_range'][0];
            $data['end_time'] = $data['time_range'][1];
        }
        unset($data['time_range']);
        // 将数组转换成字符串
        foreach (['status', 'state', 'refund_state'] as $paramKey) {
            $this->processArrayParam($data, $paramKey);
        }

        // 改为调用微服务
        try {
            $res = Api::send('/order/pay', 'GET', $data);
            $result['total'] = $res['data']['count'] ?? 0;
            $result['data'] = $res['data']['list'] ?? [];

            return $this->res_success($result, $res['msg'], $res['code']);
        } catch (Throwable $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    #[
        Apidoc\Title("获取充值订单排序信息"),
        Apidoc\Author("lwj 2024.6.13 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/RechargeOrder/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.create_time', 'label' => '创建时间'],
//                ['value' => 'a.id', 'label' => '充值流水号'],
//                ['value' => 'a.user_id', 'label' => '用户'],
//                ['value' => 'a.state', 'label' => '状态'],
//                ['value' => 'a.list_id', 'label' => '充值项'],
//                ['value' => 'a.price', 'label' => '支付金额'],
//                ['value' => 'a.currency', 'label' => '币种'],
//                ['value' => 'a.add_balance', 'label' => '加余额结果'],
//                ['value' => 'a.type', 'label' => '支付类型'],
//                ['value' => 'a.refund_state', 'label' => '退款状态'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("充值订单详情"),
        Apidoc\Author("lwj 2024.6.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/RechargeOrder/get_info"),
        Apidoc\Param(name: "recharge_id", type: "string", require: true, desc: "充值流水号"),

        Apidoc\Returned(name: "id", type: "string", desc: "充值流水号"),
        Apidoc\Returned(name: "trade_no", type: "string", desc: "外部订单编号"),
        Apidoc\Returned(name: "name", type: "int", desc: "支付项id"),
        Apidoc\Returned(name: "price", type: "int", desc: "支付金额"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付"),
        Apidoc\Returned(name: "ip", type: "string", desc: "客户ip"),
        Apidoc\Returned(name: "msg", type: "string", desc: "结果信息"),

        Apidoc\Returned(name: "add_balance", type: "int", desc: "加余额结果，1成功，2失败，3没有加币（默认3）"),
        Apidoc\Returned(name: "type", type: "int", desc: "支付类型，1充值余额，2充卡"),
        Apidoc\Returned(name: "refund_state", type: "int", desc: "退款状态，1无退款，2申请退款，3已退款，4退款失败，10已拒绝，11已取消"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "string", desc: "更新时间"),

        Apidoc\Returned(name: "user_id", type: "int", desc: "用户id"),
        Apidoc\Returned(name: "nickname", type: "string", desc: "用户昵称"),
        Apidoc\Returned(name: "phone", type: "string", desc: "用户手机号"),
        Apidoc\Returned(name: "list_id", type: "int", desc: "支付项id"),
        Apidoc\Returned(name: "title", type: "string", desc: "支付项"),

        Apidoc\Returned(name: "callback_list", type: "array", desc: "回调列表"),
    ]
    public function get_info(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'recharge_id',
            ]));

            $res = Api::send("/order/pay/info","GET",[
                'id' => $verify_data['recharge_id'],
            ]);
            return $this->res_success($res['data'], $res['msg'], $res['code']);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

}