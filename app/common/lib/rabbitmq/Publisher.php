<?php
/** @noinspection PhpUnused */
declare(strict_types=1);

namespace app\common\lib\rabbitmq;

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;
use think\facade\Log;
use Throwable;

/**
 * RabbitMQ 消息发布者
 *
 * 用于快速发布消息到RabbitMQ交换机
 * 支持fanout、direct、topic等模式
 */
class Publisher
{
    private AMQPStreamConnection $connection;
    private AMQPChannel $channel;

    /**
     * 构造函数 - 建立RabbitMQ连接
     */
    public function __construct()
    {
        $this->connect();
    }

    /**
     * 析构函数 - 关闭连接
     */
    public function __destruct()
    {
        $this->close();
    }

    /**
     * 建立 RabbitMQ 连接
     */
    private function connect(): void
    {
        try {
            // 从配置文件获取 RabbitMQ 连接配置
            $config = config('rabbitmq.connection');

            $this->connection = new AMQPStreamConnection(
                $config['host'],
                $config['port'],
                $config['user'],
                $config['password'],
                $config['vhost'],
                false, // insist
                'AMQPLAIN', // login_method
                null, // login_response
                'en_US', // locale
                $config['connection_timeout'],
                $config['read_write_timeout'],
                null, // context
                $config['keepalive'],
                $config['heartbeat']
            );

            $this->channel = $this->connection->channel();
        } catch (Throwable $e) {
            Log::error('RabbitMQ Publisher 连接失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 关闭连接
     */
    private function close(): void
    {
        try {
            if (isset($this->channel)) {
                $this->channel->close();
            }
            if (isset($this->connection)) {
                $this->connection->close();
            }
        } catch (Throwable $e) {
            Log::error('RabbitMQ Publisher 关闭连接时出错: ' . $e->getMessage());
        }
    }

    /**
     * 发布消息到队列
     *
     * @param array $config 发布配置数组
     * 必需字段：
     * - exchange: 交换机名称
     * - message: 要发布的消息内容
     * 可选字段：
     * - type: 交换机类型 (fanout|direct|topic|x-delayed-message，默认fanout)
     * - queues: 队列列表数组，如 ['queue1', 'queue2']
     * - routing_key: 路由键 (direct和topic模式需要)
     * - delay: 延时时间（秒），仅在type为x-delayed-message时有效
     * @return bool 发送是否成功
     */
    public function publish(array $config): bool
    {
        try {
            // 验证必需参数
            if (!isset($config['exchange']) || !isset($config['message'])) {
                Log::error('发布配置缺少必需参数: exchange 和 message');
                return false;
            }

            $exchange = $config['exchange'];
            $message = $config['message'];
            $type = $config['type'] ?? 'fanout';
            $queues = $config['queues'] ?? [];
            $routingKey = $config['routing_key'] ?? '';
            $delay = $config['delay'] ?? 0; // 延时时间（秒）

            // 将消息转换为JSON
            $jsonData = json_encode($message, JSON_UNESCAPED_UNICODE);
            if ($jsonData === false) {
                Log::error('消息序列化失败');
                return false;
            }

            // 创建消息属性
            $messageProperties = [
                'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT, // 消息持久化
                'content_type' => 'application/json',
                'timestamp' => time()
            ];

            // 如果是延时消息，添加延时头部
            if ($type === 'x-delayed-message' && $delay > 0) {
                $messageProperties['application_headers'] = new AMQPTable([
                    'x-delay' => $delay * 1000 // 延时时间（毫秒）
                ]);
            }

            // 创建消息对象
            $amqpMessage = new AMQPMessage($jsonData, $messageProperties);

            // 根据交换机类型发布消息
            switch ($type) {
                case 'direct':
                    $this->publishDirect($exchange, $type, $queues, $amqpMessage, $routingKey);
                    Log::info("消息已发送到交换机 {$exchange}，类型 {$type}，路由键 {$routingKey}: {$jsonData}");
                    break;
                case 'topic':
                    $this->publishTopic($exchange, $type, $queues, $amqpMessage, $routingKey);
                    Log::info("消息已发送到交换机 {$exchange}，类型 {$type}，路由键 {$routingKey}: {$jsonData}");
                    break;
                case 'fanout':
                default:
                    $this->publishFanout($exchange, $type, $queues, $amqpMessage);
                    Log::info("消息已发送到交换机 {$exchange}，类型 {$type}: {$jsonData}");
                    break;
            }

            return true;
        } catch (Throwable $e) {
            Log::error('发送消息失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用扇出模式发布消息
     *
     * @param string $exchange 交换机名称
     * @param string $exchangeType 交换机类型
     * @param array $queues 队列列表
     * @param AMQPMessage $message 消息对象
     */
    private function publishFanout(string $exchange, string $exchangeType, array $queues, AMQPMessage $message): void
    {
        // 声明交换机（如果不存在则创建）
        $this->channel->exchange_declare(
            $exchange,
            $exchangeType,  // 交换机类型
            false,          // passive
            true,           // durable - 持久化
            false           // auto_delete
        );

        // 声明队列并绑定到交换机
        $this->declareAndBindQueues($exchange, $queues);

        // 发布消息到交换机
        $this->channel->basic_publish($message, $exchange);
    }

    /**
     * 使用直连模式发布消息
     *
     * @param string $exchange 交换机名称
     * @param string $exchangeType 交换机类型
     * @param array $queues 队列列表
     * @param AMQPMessage $message 消息对象
     * @param string $routingKey 路由键
     */
    private function publishDirect(string $exchange, string $exchangeType, array $queues, AMQPMessage $message, string $routingKey): void
    {
        // 声明交换机（如果不存在则创建）
        $this->channel->exchange_declare(
            $exchange,
            $exchangeType,  // 交换机类型
            false,          // passive
            true,           // durable - 持久化
            false           // auto_delete
        );

        // 声明队列并绑定到交换机（direct模式使用路由键绑定）
        $this->declareAndBindQueues($exchange, $queues, $routingKey);

        // 发布消息到交换机
        $this->channel->basic_publish($message, $exchange, $routingKey);
    }

    /**
     * 使用主题模式发布消息
     *
     * @param string $exchange 交换机名称
     * @param string $exchangeType 交换机类型
     * @param array $queues 队列列表
     * @param AMQPMessage $message 消息对象
     * @param string $routingKey 路由键
     */
    private function publishTopic(string $exchange, string $exchangeType, array $queues, AMQPMessage $message, string $routingKey): void
    {
        // 声明交换机（如果不存在则创建）
        $this->channel->exchange_declare(
            $exchange,
            $exchangeType,  // 交换机类型
            false,          // passive
            true,           // durable - 持久化
            false           // auto_delete
        );

        // 声明队列并绑定到交换机（topic模式使用路由键绑定）
        $this->declareAndBindQueues($exchange, $queues, $routingKey);

        // 发布消息到交换机
        $this->channel->basic_publish($message, $exchange, $routingKey);
    }

    /**
     * 声明队列并绑定到交换机
     *
     * @param string $exchange 交换机名称
     * @param array $queues 队列列表
     * @param string $routingKey 路由键（可选，用于direct和topic模式）
     */
    private function declareAndBindQueues(string $exchange, array $queues, string $routingKey = ''): void
    {
        foreach ($queues as $queueName) {
            // 设置死信队列相关参数
            $deadLetterExchange = $exchange . '.dead';
            $deadLetterQueue = $queueName . '.dead';
            $deadLetterRoutingKey = $routingKey ? $routingKey . '.dead' : '';

            // 先声明死信交换机和死信队列
            $this->setupDeadLetterQueue($deadLetterExchange, $deadLetterQueue, $deadLetterRoutingKey);

            // 声明主队列，配置死信参数
            $this->channel->queue_declare(
                $queueName,
                false,  // passive
                true,   // durable - 持久化
                false,  // exclusive
                false,  // auto_delete
                false,  // nowait
                new AMQPTable([
                    'x-dead-letter-exchange' => $deadLetterExchange,
                    'x-dead-letter-routing-key' => $deadLetterRoutingKey ?: $deadLetterQueue
                ])
            );

            // 绑定主队列到交换机
            if ($routingKey !== '') {
                // direct和topic模式使用路由键绑定
                $this->channel->queue_bind($queueName, $exchange, $routingKey);
            } else {
                // fanout模式不需要路由键
                $this->channel->queue_bind($queueName, $exchange);
            }

            Log::info("队列 {$queueName} 已声明并绑定到交换机 {$exchange}" . ($routingKey ? "，路由键: {$routingKey}" : ""));
            Log::info("死信队列 {$deadLetterQueue} 已配置，死信交换机: {$deadLetterExchange}");
        }
    }

    /**
     * 设置死信队列
     *
     * @param string $deadLetterExchange 死信交换机名称
     * @param string $deadLetterQueue 死信队列名称
     * @param string $deadLetterRoutingKey 死信路由键
     */
    private function setupDeadLetterQueue(string $deadLetterExchange, string $deadLetterQueue, string $deadLetterRoutingKey): void
    {
        // 声明死信交换机
        $this->channel->exchange_declare(
            $deadLetterExchange,
            'direct',  // 死信交换机使用direct类型
            false,     // passive
            true,      // durable - 持久化
            false      // auto_delete
        );

        // 声明死信队列
        $this->channel->queue_declare(
            $deadLetterQueue,
            false,  // passive
            true,   // durable - 持久化
            false,  // exclusive
            false   // auto_delete
        );

        // 绑定死信队列到死信交换机
        $this->channel->queue_bind(
            $deadLetterQueue,
            $deadLetterExchange,
            $deadLetterRoutingKey ?: $deadLetterQueue
        );

        Log::info("死信交换机 {$deadLetterExchange} 和死信队列 {$deadLetterQueue} 已设置完成");
    }

    /**
     * 快速发布消息的静态方法
     *
     * @param array $config 发布配置数组
     * @return bool 发送是否成功
     */
    public static function quickPublish(array $config): bool
    {
        try {
            $publisher = new self();
            return $publisher->publish($config);
        } catch (Throwable $e) {
            Log::error('发布消息失败: ' . $e->getMessage());
            return false;
        }
    }
}
