<?php

namespace app\ms;

use Chenbingji\Tool\http\HttpClient;
use Exception;
use think\facade\Log;
use app\common\lib\rabbitmq\Publisher;

class Api{
    public static function send($url, $method = 'POST', $data = [],$headers = [],$key = '',$forceQueue = false,$notQueue = false)
    {
        $method = strtoupper($method);
        // 获取登录用户(管理员/小程序用户)token
        $token = request()->header('Authorization');
        $headers = array_merge([
            'Content-Type' => 'application/json; charset=utf-8',
            'Authorization' => $token
        ],$headers);
        try {
            // 如果强制进入队列，则直接进入队列
            if ($forceQueue) {
                return self::pushToQueue($url, $method, $data, $headers, $key);
            }
            // 先尝试同步执行
            return self::executeRequest($url, $method, $data, $headers);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            // GET请求不进入队列 或者 $notQueue不进入队列,仅尝试一次
            if ($method !== 'GET' || $notQueue) {
                return self::pushToQueue($url, $method, $data, $headers, $key);
            }
            return ['status' => 'failed'];
        }
    }

    // Api.php 中的 pushToQueue 方法
    private static function pushToQueue($url, $method, $data, $headers, $key): array
    {
        // 使用微秒级时间戳
        $timestamp = time();

        // 生成缓存key
        $cacheKey = self::generateCacheKey($url, $method, $data, $key);
        cache("sync_api:{$cacheKey}", $timestamp, 259200); // 缓存 3天

        // 直接推送到延时队列，延时30秒后开始第一次重试
        $success = Publisher::quickPublish([
            'exchange' => 'api_sync',
            'type' => 'x-delayed-message',
            'queues' => ['api_sync'],
            'routing_key' => 'api.sync',
            'message' => [
                'url'     => $url,
                'method'  => $method,
                'data'    => $data,
                'headers' => $headers,
                'key'     => $cacheKey,
                'timestamp' => $timestamp,
            ]
        ]);

        if ($success) {
            Log::info("API请求同步失败，已推入延时队列，30秒后开始重试: {$url}");
            return ['status' => 'queued_with_delay'];
        } else {
            Log::error('RabbitMQ 延时消息发布失败');
            return ['status' => 'failed'];
        }
    }

    /**
     * @throws Exception
     */
    public static function executeRequest($url, $method = 'POST', $data = [], $headers = []): array
    {
        $httpClient = new HttpClient();
        $api = env('MY.API_GATEWAY');

        if ($method === 'GET') {
            // 对GET请求参数进行URL编码
            $encodedData = array_map(function($value) {
                return is_string($value) ? urlencode($value) : $value;
            }, $data);
            $httpClient->setQuery($encodedData);
        } else {
            $httpClient->setBody(json_encode($data));
        }

        $httpClient
            ->setUrl($api . $url)
            ->setMethod($method)
            ->setHeader($headers);

        $response = $httpClient->sendRequest();
        // 记录请求和响应日志
        Log::debug(sprintf("url = %s", $httpClient->getUrl()));
        Log::debug(sprintf("method = %s", $httpClient->getMethod()));
        Log::debug(sprintf("headers = %s", json_encode_cn($headers)));
        Log::debug(sprintf("request body = %s", json_encode_cn($data)));
        Log::debug(sprintf("response body = %s", json_encode_cn($response->analysisJsonBody())));
        Log::debug(sprintf("failedMsg = %s", json_encode_cn($response->failedMsg)));
        // 如果响应体为空表示请求失败,抛出异常
        if (empty($response->analysisJsonBody())){
            throw new Exception('response body is empty');
        }

        return $response->analysisJsonBody();
    }

    /**
     * 生成缓存key
     * @param string $url 请求URL
     * @param string $method 请求方法
     * @param array $data 请求数据
     * @param string $key 主键字段，多个字段用逗号分隔
     * @return string
     */
    private static function generateCacheKey(string $url,string $method, array $data, string $key): string
    {
        // 获取主键字段列表
        $keyFields = $key ? explode(',', $key) : [array_key_first($data)];

        // 获取主键值
        $keyValues = [];
        foreach ($keyFields as $field) {
            $field = trim($field);
            if (isset($data[$field])) {
                $keyValues[] = $data[$field];
            }
        }

        // 获取所有字段名列表
        $fieldNames = array_keys($data);

        // 组合所有数据，只包含字段名而不包含值
        $hashData = [
            'url' => $url,
            'method' => $method,
            'fields' => $fieldNames,
            'key_values' => $keyValues
        ];

        // 生成MD5哈希
        return md5(json_encode($hashData, JSON_UNESCAPED_UNICODE));
    }
}